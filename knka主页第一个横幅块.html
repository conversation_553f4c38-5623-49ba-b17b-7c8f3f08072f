<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>KNKA Dehumidifier Showcase</title>
<style>
  /* 基本样式 */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', Helvetica, Arial, sans-serif;
    color: #1d1d1f;
    line-height: 1.47059;
    overflow-x: hidden;
  }
  
  .aphm1-iphone-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 40px 20px 0;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    transition: transform 0.3s ease;
    background-color: #f5f5f7;
    aspect-ratio: 2.1 / 1;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
  }
  
  .aphm1-iphone-container:hover {
    transform: scale(1.09); /* 将元素放大2% */
  }
  
  .aphm1-content-wrapper {
    margin-bottom: 20px;
    z-index: 2;
    padding-top: 3%;
    width: 100%;
  }
  
  .aphm1-title {
    font-size: 48px;
    font-weight: 600;
    margin: 0 0 10px;
    color: #1d1d1f;
  }
  
  .aphm1-subtitle {
    font-size: 28px;
    font-weight: 400;
    margin: 0 0 25px;
    color: #1d1d1f;
  }
  
  .aphm1-buttons-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
  }
  
  .aphm1-button {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 400;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .aphm1-button-primary {
    background-color: #0071e3;
    color: white;
  }
  
  .aphm1-button-primary:hover {
    background-color: #0062c3;
    transform: scale(1.05);
  }
  
  .aphm1-button-secondary {
    background-color: white;
    color: #0071e3;
    border: 1px solid #0071e3;
  }
  
  .aphm1-button-secondary:hover {
    background-color: rgba(0, 113, 227, 0.1);
    transform: scale(1.05);
  }
  
  .aphm1-tagline {
    font-size: 18px;
    margin: 0;
    color: #1d1d1f;
  }
  
  .aphm1-highlight {
    background: linear-gradient(90deg, #0071e3, #bf4ec8);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 500;
  }
  
  .aphm1-image-container {
    width: 100%;
    height: auto;
    position: relative;
    flex-grow: 1;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    overflow: hidden;
  }
  
  .aphm1-iphone-image {
    width: 65%;
    max-width: 900px;
    max-height: 100%;
    object-fit: contain;
    object-position: bottom center;
    display: block;
    margin-bottom: -2%;
  }
  
  /* 响应式设计 */
  @media (min-width: 1601px) {
    .aphm1-iphone-image {
      width: 55%;
    }
  }
  
  @media (max-width: 1600px) and (min-width: 1401px) {
    .aphm1-iphone-image {
      width: 60%;
    }
  }
  
  @media (max-width: 1400px) and (min-width: 1201px) {
    .aphm1-iphone-image {
      width: 65%;
    }
  }
  
  @media (max-width: 1200px) and (min-width: 993px) {
    .aphm1-iphone-container {
      aspect-ratio: 2 / 1;
    }
    
    .aphm1-content-wrapper {
      padding-top: 2.5%;
      margin-bottom: 15px;
    }
    
    .aphm1-iphone-image {
      width: 70%;
      margin-bottom: -1%;
    }
  }
  
  @media (max-width: 992px) and (min-width: 769px) {
    .aphm1-iphone-container {
      padding-bottom: 20px;
      aspect-ratio: 1.9 / 1;
    }
    
    .aphm1-content-wrapper {
      padding-top: 2%;
      margin-bottom: 10px;
    }
    
    .aphm1-image-container {
      height: auto;
    }
    
    .aphm1-iphone-image {
      width: 75%;
      margin-bottom: 0;
    }
  }
  
  @media (max-width: 768px) {
    .aphm1-iphone-container {
      aspect-ratio: auto;
      min-height: 500px;
      padding-bottom: 30px;
    }
    
    .aphm1-content-wrapper {
      padding-top: 5%;
    }
    
    .aphm1-title {
      font-size: 36px;
    }
    
    .aphm1-subtitle {
      font-size: 22px;
    }
    
    .aphm1-buttons-container {
      flex-direction: column;
      gap: 10px;
    }
    
    .aphm1-button {
      width: 80%;
      margin: 0 auto;
    }
    
    .aphm1-image-container {
      margin-top: 20px;
    }
    
    .aphm1-iphone-image {
      width: 90%;
      margin-bottom: 0;
    }
  }
  
  @media (max-width: 480px) {
    .aphm1-iphone-container {
      min-height: 450px;
    }
    
    .aphm1-title {
      font-size: 28px;
    }
    
    .aphm1-subtitle {
      font-size: 18px;
    }
    
    .aphm1-tagline {
      font-size: 16px;
    }
    
    .aphm1-iphone-image {
      width: 100%;
    }
  }

  /* 弹窗样式 - 优化后的样式 */
  /* 毛玻璃背景 */
  .aphm1-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  /* 弹出卡片样式 */
  .aphm1-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    width: 90%;
    max-width: 1000px;
    max-height: 85vh;
    background-color: #fff;
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-radius: 16px;
    overflow: hidden;
  }

  /* 弹出卡片内容 */
  .aphm1-modal-content {
    padding: 24px;
    height: 100%;
    max-height: 85vh;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    display: flex;
    flex-direction: column;
  }

  /* 隐藏WebKit浏览器的滚动条 */
  .aphm1-modal-content::-webkit-scrollbar {
    display: none;
  }

  /* 产品类别标题 */
  .aphm1-modal-category {
    font-size: 14px;
    line-height: 1.42859;
    font-weight: 400;
    letter-spacing: -0.016em;
    color: #86868b;
    margin-bottom: 12px;
    margin-left: 4px;
  }

  /* 弹出卡片主体 - 左右布局 */
  .aphm1-modal-body {
    display: flex;
    flex-direction: row;
    gap: 32px;
    width: 100%;
    min-height: 380px;
  }

  /* 左侧产品图片区域 */
  .aphm1-modal-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  /* 产品图片容器 */
  .aphm1-modal-image {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 8px;
  }

  .aphm1-modal-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  /* 右侧产品信息区域 */
  .aphm1-modal-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 4px 0;
  }

  /* 产品标题 */
  .aphm1-modal-product-title {
    font-size: 36px;
    line-height: 1.1;
    font-weight: 600;
    letter-spacing: -0.002em;
    color: #1d1d1f;
    margin: 0 0 16px 0;
  }

  /* 价格和购买按钮容器 */
  .aphm1-modal-price-buy-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  /* 产品价格 */
  .aphm1-modal-price {
    font-size: 15px;
    line-height: 1.4;
    font-weight: 400;
    color: #1d1d1f;
    margin: 0;
    max-width: 80%;
  }

  /* 弹出卡片内的购买按钮 */
  .aphm1-modal-buy {
    background: #0071e3;
    color: #fff;
    border-radius: 980px;
    font-size: 15px;
    line-height: 1.47059;
    font-weight: 500;
    padding: 6px 16px;
    text-decoration: none;
    text-align: center;
    display: inline-block;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
    width: 80px;
  }

  .aphm1-modal-buy:hover {
    background-color: #0077ed;
  }

  /* 产品特性列表 */
  .aphm1-modal-features {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  /* 产品特性项 */
  .aphm1-feature-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-top: 1px solid #d2d2d7;
  }

  .aphm1-feature-item:first-child {
    border-top: none;
    padding-top: 0;
  }

  /* 特性图标 */
  .aphm1-feature-icon {
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }

  .aphm1-feature-icon svg {
    width: 24px;
    height: 24px;
  }

  /* 特性文本 */
  .aphm1-feature-content {
    flex: 1;
  }

  .aphm1-feature-title {
    font-size: 15px;
    line-height: 1.4;
    font-weight: 600;
    margin-bottom: 4px;
    color: #1d1d1f;
  }

  .aphm1-feature-text {
    font-size: 15px;
    line-height: 1.4;
    color: #1d1d1f;
  }

  /* 上标样式 */
  .aphm1-superscript {
    font-size: 0.6em;
    vertical-align: super;
  }

  /* 关闭按钮 */
  .aphm1-modal-close {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    z-index: 10;
    transition: background-color 0.2s ease;
    padding: 0;
    margin: 0;
  }

  .aphm1-modal-close:hover {
    background-color: #000;
  }

  .aphm1-modal-close svg {
    width: 10px;
    height: 10px;
    display: block;
  }

  /* 显示弹出卡片和背景 */
  .aphm1-modal.active {
    display: block;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  .aphm1-modal-backdrop.active {
    display: block;
    opacity: 1;
  }

  /* 响应式调整 */
  @media only screen and (max-width: 1068px) {
    .aphm1-modal-product-title {
      font-size: 28px;
    }
    
    .aphm1-modal-body {
      gap: 24px;
    }
  }

  @media only screen and (max-width: 734px) {
    .aphm1-modal-product-title {
      font-size: 24px;
      margin-bottom: 12px;
    }
    
    .aphm1-modal-content {
      padding: 20px;
    }
    
    /* 移动端弹窗布局调整为垂直方向 */
    .aphm1-modal-body {
      flex-direction: column;
      gap: 16px;
      min-height: auto;
    }
    
    /* 移动端下图片区域添加底部间距 */
    .aphm1-modal-left {
      margin-bottom: 0;
      max-height: 200px;
    }
    
    .aphm1-feature-item {
      padding: 10px 0;
    }
    
    .aphm1-feature-icon {
      width: 28px;
      height: 28px;
    }
    
    .aphm1-feature-icon svg {
      width: 20px;
      height: 20px;
    }
    
    .aphm1-feature-title {
      font-size: 14px;
    }
    
    .aphm1-feature-text {
      font-size: 14px;
    }
  }
</style>
</head>
<body>

<div class="aphm1-iphone-container">
  <div class="aphm1-content-wrapper">
    <h1 class="aphm1-title">KNKA Dehumidifier</h1>
    <h2 class="aphm1-subtitle">PD22SC</h2>
    
    <div class="aphm1-buttons-container">
      <a href="#" class="aphm1-button aphm1-button-primary" data-modal="dehumidifier-modal">Learn more</a>
      <a href="#" class="aphm1-button aphm1-button-secondary">Shop Here</a>
    </div>
    
    <p class="aphm1-tagline"><span class="aphm1-highlight">Keep Natural Keep Advancing</span>.</p>
  </div>
  
  <div class="aphm1-image-container">
    <img src="https://knkalife.com/wp-content/uploads/2025/05/lQLPJx_rqHv0yQvNBDjNCgCwpLsXSumpf3MIBmRmbcETAA_2560_1080-e1747298640161.png" alt="KNKA Dehumidifier PD22SC" class="aphm1-iphone-image">
  </div>
</div>

<!-- 毛玻璃背景 -->
<div class="aphm1-modal-backdrop" id="modal-backdrop"></div>

<!-- Dehumidifier PD22SC 弹出卡片 -->
<div class="aphm1-modal" id="dehumidifier-modal">
  <div class="aphm1-modal-content">
    <p class="aphm1-modal-category">Dehumidifier</p>
    
    <div class="aphm1-modal-body">
      <div class="aphm1-modal-left">
        <div class="aphm1-modal-image">
          <img src="https://knkalife.com/wp-content/uploads/2025/05/主图1-1.jpg" alt="Dehumidifier PD22SC">
        </div>
      </div>
      
      <div class="aphm1-modal-right">
        <h2 class="aphm1-modal-product-title">PD22SC</h2>
        <div class="aphm1-modal-price-buy-container">
          <p class="aphm1-modal-price">Let the unit stand for 24 hours after delivery to ensure optimal performance and long-term reliability.</p>
          <a href="#" class="aphm1-modal-buy">Buy</a>
        </div>
        
        <div class="aphm1-modal-features">
          <div class="aphm1-feature-item">
            <div class="aphm1-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 7L12 3L4 7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 7V17L12 21L4 17V7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11L20 7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11L4 7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 11V21" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm1-feature-content">
              <p class="aphm1-feature-text"><strong>Smart Control. Seamless Comfort.</strong><br>Set humidity, fan speed, timer, and more—all with a tap. It auto-adjusts, so you don't have to.</p>
            </div>
          </div>
          
          <div class="aphm1-feature-item">
            <div class="aphm1-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 7H18C19.1046 7 20 7.89543 20 9V17C20 18.1046 19.1046 19 18 19H6C4.89543 19 4 18.1046 4 17V9C4 7.89543 4.89543 7 6 7Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 13H15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M15 7V5C15 3.89543 14.1046 3 13 3H11C9.89543 3 9 3.89543 9 5V7" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm1-feature-content">
              <p class="aphm1-feature-text"><strong>Powerful, Precise. Built for 2,500 Sq. Ft.</strong><br>Removes up to 34 pints daily to keep your entire home comfortably dry—quietly and efficiently.</p>
            </div>
          </div>
          
          <div class="aphm1-feature-item">
            <div class="aphm1-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4C14.7614 4 17 6.23858 17 9C17 11.7614 14.7614 14 12 14C9.23858 14 7 11.7614 7 9C7 6.23858 9.23858 4 12 4Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 14V20" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 17H15" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm1-feature-content">
              <p class="aphm1-feature-text"><strong>Three Modes, Total Control.</strong><br>DEHU for custom humidity. DRY for faster clothes drying. CONT for nonstop moisture removal.</p>
            </div>
          </div>
          
          <div class="aphm1-feature-item">
            <div class="aphm1-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 12V19" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 5V5.01" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm1-feature-content">
              <p class="aphm1-feature-text"><strong>Dual Drainage, Zero Interruptions.</strong><br>Auto-stop with full tank. Or go hands-free with continuous drainage via the included hose.</p>
            </div>
          </div>
          
          <div class="aphm1-feature-item">
            <div class="aphm1-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 2V4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 20V22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 4.93005L6.33993 6.34005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 17.66L19.0699 19.07" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12H4" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M20 12H22" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4.92993 19.07L6.33993 17.66" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17.6599 6.34005L19.0699 4.93005" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm1-feature-content">
              <p class="aphm1-feature-text"><strong>Whisper-Quiet, Always Aware.</strong><br>Runs as quiet as a whisper. The LED ring subtly shifts color to show real-time humidity—even when off.</p>
            </div>
          </div>
          
          <div class="aphm1-feature-item">
            <div class="aphm1-feature-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 10L19 14L15 18" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 14H9" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1d1d1f" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="aphm1-feature-content">
              <p class="aphm1-feature-text"><strong>Designed to Move, Built to Last.</strong><br>360° wheels, soft-touch handle, and a washable filter—every detail made for effortless living.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <button class="aphm1-modal-close" aria-label="Close">
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L11 11M11 1L1 11" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<script>
  // Elementor兼容的初始化函数
  function initializeElements() {
    console.log('正在初始化元素...');

    // 获取所有元素
    const learnMoreButtons = document.querySelectorAll('[data-modal]');
    const modalBackdrop = document.getElementById('modal-backdrop');
    const modals = document.querySelectorAll('.aphm1-modal');
    const closeButtons = document.querySelectorAll('.aphm1-modal-close');
    let buyButton = document.querySelector('.aphm1-button-secondary'); // 主页购买按钮
    let modalBuyButton = document.querySelector('.aphm1-modal-buy'); // 模态框购买按钮

    console.log('buyButton found:', buyButton);
    console.log('modalBuyButton found:', modalBuyButton);

    // 如果主要按钮不存在，等待一段时间后重试
    if (!buyButton) {
      console.log('buyButton未找到，1秒后重试...');
      setTimeout(initializeElements, 1000);
      return;
    }
    
    // 打开弹出卡片
    learnMoreButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const modalId = this.getAttribute('data-modal');
        const modal = document.getElementById(modalId);
        
        // 显示毛玻璃背景和弹出卡片
        modalBackdrop.classList.add('active');
        modal.classList.add('active');
        
        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
      });
    });
    
    // 关闭弹出卡片
    function closeModal() {
      modalBackdrop.classList.remove('active');
      modals.forEach(modal => {
        modal.classList.remove('active');
      });
      
      // 恢复背景滚动
      document.body.style.overflow = '';
    }
    
    // 点击关闭按钮
    closeButtons.forEach(button => {
      button.addEventListener('click', closeModal);
    });
    
    // 点击背景关闭弹出卡片
    modalBackdrop.addEventListener('click', closeModal);
    
    // 阻止弹出卡片内点击事件冒泡到背景
    modals.forEach(modal => {
      modal.addEventListener('click', function(e) {
        e.stopPropagation();
      });
    });
    
    // ESC键关闭弹出卡片
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        closeModal();
      }
    });
    
    // 1. 扩展的多语言购买链接配置
    const buyLinks = {
        // 英语国家细分
        'en-US': 'https://www.amazon.com/dp/B0DR7TZGV8',
        'en-GB': 'https://amazon.co.uk/knka-dehumidifier', 
        'en-CA': 'https://www.amazon.ca/dp/B0DR7TZGV8',
        'en-AU': 'https://amazon.com.au/knka-dehumidifier',
        'en-NZ': 'https://amazon.com.au/knka-dehumidifier', // 新西兰用澳洲链接
        
        // 其他语言保持不变
        'es': 'https://www.xibanya.com',
        'de': 'https://www.amazon.de/dp/B0DZXKLNVL',
        'fr': 'https://www.faguo.com',
        'it': 'https://www.yidaliyu.com',
        
        // 默认英语
        'en': 'https://www.amazon.com/dp/B0DR7TZGV8'
    };

    // 2. 获取用户国家的函数
    async function getUserCountry() {
        try {
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            return data.country_code;
        } catch (error) {
            console.log('IP检测失败，使用默认美国链接');
            return 'US';
        }
    }

    // 3. 更新的购买按钮链接函数（同时更新主页和模态框按钮）
    async function updateBuyButtonLink() {
        const currentBuyButton = document.querySelector('.aphm1-button-secondary');
        const currentModalBuyButton = document.querySelector('.aphm1-modal-buy');

        if (!currentBuyButton) {
            console.error("Buy button with class '.aphm1-button-secondary' not found.");
            return;
        }

        const currentLang = document.documentElement.lang.split('-')[0];
        let targetLink;

        // 先设置默认链接，避免显示#
        const defaultLink = buyLinks['en'] || 'https://amazon.com/knka-dehumidifier';
        currentBuyButton.href = defaultLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = defaultLink;
        }
        console.log('设置默认链接:', defaultLink);

        if (currentLang === 'en') {
            // 英语时进一步检测国家
            try {
                const country = await getUserCountry();
                const langCountry = `en-${country}`;
                targetLink = buyLinks[langCountry] || buyLinks['en'];
                console.log(`English detected. Country: ${country}, Using link: ${targetLink}`);
            } catch (error) {
                console.log('获取国家信息失败，使用默认英语链接');
                targetLink = buyLinks['en'];
            }
        } else {
            // 非英语直接使用语言代码
            targetLink = buyLinks[currentLang] || buyLinks['en'];
            console.log(`Language: ${currentLang}, Using link: ${targetLink}`);
        }

        // 同时更新两个按钮的链接
        currentBuyButton.href = targetLink;
        if (currentModalBuyButton) {
            currentModalBuyButton.href = targetLink;
            console.log('模态框按钮链接也已更新:', currentModalBuyButton.href);
        }
        console.log('主页按钮最终链接:', currentBuyButton.href);
    }

    // 4. 使用 MutationObserver 监视 <html> 标签的 lang 属性变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                updateBuyButtonLink(); // 当 lang 属性变化时，调用更新函数
            }
        });
    });

    // 5. 配置并启动观察器，让它开始监视
    observer.observe(document.documentElement, {
        attributes: true // 我们只关心属性的变化
    });

    // 6. 页面加载完成后，立即执行一次，以正确设置初始的购买链接
    updateBuyButtonLink();
  }

  // 多种方式确保代码执行，适配Elementor环境
  document.addEventListener('DOMContentLoaded', initializeElements);
  window.addEventListener('load', initializeElements);

  // 如果页面已经加载完成，立即执行
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    setTimeout(initializeElements, 100);
  }
</script>

</body>
</html>
